图片上传核心流程
1. 预签名URL获取
export async function getPresignedUrl(fileType: 'image/png' | 'text/html', fileName?: string): Promise<PresignedUrlResponse> {
  const workerUrl = 'https://get-s3-url.pjharvey071.workers.dev'

  const requestBody = { fileType }
  if (fileName) {
    requestBody.fileName = fileName
  }

  const response = await fetch(workerUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  const data = await response.json()
  return data // { uploadUrl: string, publicUrl: string }
}
关键点：

向Cloudflare Worker发送POST请求
传递文件类型（image/png）和可选的文件名
Worker返回两个URL：uploadUrl（用于上传）和publicUrl（用于访问）
2. 直接上传到S3
export async function uploadFileToS3(blob: Blob, fileType: 'image/png' | 'text/html', fileName?: string): Promise<string> {
    // 获取预签名URL
    const { uploadUrl, publicUrl } = await getPresignedUrl(fileType, fileName)
    
    // 直接上传到S3
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': fileType,
      },
      body: blob,
    })
  
    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`)
    }
  
    return publicUrl
  }
  关键点：

使用PUT方法直接向S3上传
设置正确的Content-Type
直接传递blob数据作为请求体
返回可公开访问的URL
3. 图片生成和上传的完整流程

// 1. 使用html2canvas生成图片
const canvas = await html2canvas(elementToCapture as HTMLElement, {
    backgroundColor: '#ffffff',
    scale: 2,
    useCORS: true,
    allowTaint: true,
  })
  
  // 2. 转换为blob
  const blob = await new Promise<Blob>((resolve) => {
    canvas.toBlob((blob) => resolve(blob!), 'image/png', 1.0)
  })
  
  // 3. 上传到S3
  const imageUrl = await uploadFileToS3(blob, 'image/png', fileName)